---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: qa-backend
  namespace: apps
  annotations:
    reloader.stakater.com/auto: "true"
spec:
  replicas: 1
  revisionHistoryLimit: 0
  selector:
    matchLabels:
      app: qa-backend
  template:
    metadata:
      labels:
        app: qa-backend
    spec:
      affinity:
        nodeAffinity:
          requiredDuringSchedulingIgnoredDuringExecution:
            nodeSelectorTerms:
              - matchExpressions:
                  - key: assignment
                    operator: In
                    values:
                      - qa-tool
      tolerations:
        - key: "qa-tool"
          operator: Equal
          value: "true"
          effect: "NoExecute"
      serviceAccountName: qa-backend
      volumes:
        - name: secrets-store-inline
          csi:
            driver: secrets-store.csi.k8s.io
            readOnly: true
            volumeAttributes:
              secretProviderClass: qa-backend
      dnsPolicy: ClusterFirst
      containers:
        - name: qa-backend
          image: ************.dkr.ecr.us-east-2.amazonaws.com/qa-backend:f4d7fd1e7f6cec50d29388980e098f33e25896f8
          resources:
            limits:
              memory: 1000Mi
              cpu: 500m
            requests:
              memory: 200Mi
              cpu: 200m
          envFrom:
            - secretRef:
                name: qa-backend
          env:
            - name: API_HOST
              value: "0.0.0.0"
            - name: API_PORT
              value: "8000"
            - name: RABBITMQ_HOST
              value: rabbitmq.rabbitmq.svc.cluster.local
            - name: RABBITMQ_PORT
              value: "5672"
            - name: RABBITMQ_DEFAULT_USER
              value: user
            - name: RABBITMQ_DEFAULT_PASS
              value: Awwmm8FDzgcShYXS
            - name: RABBITMQ_DEFAULT_VHOST
              value: "/"
            - name: RABBITMQ_QUEUE_SUB_PACKET_INCOMING
              value: "to_backend_qa"
            - name: RABBITMQ_QUEUE_SUB_PACKET_SKIPPED_INCOMING
              value: "to_backend_over_qa"
            - name: RABBITMQ_QUEUE_SUB_PACKET_STATUS
              value: "packet_status"
            - name: RABBITMQ_QUEUE_SUB_PACKET_SCREENSHOT
              value: "to_screenshot_postprocess"
            - name: RABBITMQ_QUEUE_PUB
              value: "from_backend_qa"
            - name: RABBITMQ_QUEUE_PUB_TO_QA_POSTPROCESSOR
              value: "atom_queue_from_backend_qa"
            - name: FRONTEND_URL 
              value: "https://qa-frontend-dev.atomadvantage.ai"
            - name: SOURCE_SECRET_KEY
              value: "zqnR3gKxR2h_xZ9YqL8n7XgP6sW5tY3jK2mN1bC0vFg="
            - name: AWS_S3_BUCKET
              value: "atom-advantage-packets-dev"
            - name: ACCESS_TOKEN_EXPIRE_MINUTES
              value: "60"
          volumeMounts:
            - name: secrets-store-inline
              mountPath: /secrets
