import json
import os
import sys
import unittest
from datetime import datetime
from io import BytesIO
from unittest.mock import <PERSON><PERSON><PERSON>, <PERSON><PERSON>, patch
from uuid import UUID

import pytest
from PIL import Image

# Add parent directory to path to import modules
current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(current_dir)
sys.path.append(parent_dir)

# Import the function to test
from metadata_extractor.metadata_extractor import extract_metadata


class TestMetadataExtractor:
    """Test class for the extract_metadata function."""

    @pytest.fixture
    def mock_splitted_document(self):
        """Create a mock splitted document for testing."""
        mock_doc = Mock()
        mock_doc.text_ocr = "Sample OCR text for testing"
        mock_doc.document_type = "RFA"
        mock_doc.uuid = UUID("12345678-1234-5678-1234-************")
        mock_doc.parent_document_pages = [[1, 2, 3]]
        return mock_doc

    @pytest.fixture
    def mock_pdf_file(self):
        """Create a mock PDF file stream for testing."""
        # This is a simplified mock of a PDF file stream
        return b"Mock PDF file content"

    @pytest.fixture
    def mock_jsonformat(self):
        """Mock the JSONFormat class."""
        with patch("metadata_extractor.metadata_extractor.JSONFormat") as mock:
            # Set up the mock to return a predefined structure
            mock.GetFieldsForDocument.return_value = {
                "docName": "",
                "docLink": "",
                "docType": "RFA",
                "docConfidence": None,
                "pageRefStart": None,
                "pageRefEnd": None,
                "pagesRef": None,
                "pagesRefOrig": None,
                "claimNumber": "",
                "isDuplicated": False,
                "namingData": {
                    "claimNumber": "",
                    "patientName": "",
                    "senderName": "",
                    "docDate": "",
                    "docReceivedDate": "",
                    "patientFirstName": "",
                    "patientMiddleName": "",
                    "patientLastName": "",
                    "docState": "",
                    "providerOrFacilityName": "",
                },
                "metaData": {
                    "docDate": "",
                    "docReceivedDate": "",
                    "senderName": "",
                    "isEnglishLanguage": None,
                    "isHandwritten": None,
                    "claim": {
                        "claimNumber": "",
                        "dateOfInjuryFrom": "",
                        "dateOfInjuryThrough": "",
                        "jurisState": "",
                    },
                    "claimant": {
                        "firstName": "",
                        "lastName": "",
                        "middleName": "",
                        "claimantName": "",
                    },
                }
            }
            mock.metadata_required_fileds = {
                "rfa": {
                    "docDate": "",
                    "senderName": "",
                    "claim": {
                        "claimNumber": "",
                    },
                    "claimant": {
                        "claimantName": "",
                    },
                }
            }
            yield mock

    @pytest.fixture
    def mock_fill_mapped_fields(self):
        """Mock the fill_mapped_fields function."""
        with patch("metadata_extractor.metadata_extractor.fill_mapped_fields") as mock:
            yield mock

    @pytest.fixture
    def mock_extract_metadata_from_rfa(self):
        """Mock the extract_metadata_from_rfa function."""
        with patch("metadata_extractor.metadata_extractor.extract_metadata_from_rfa") as mock:
            mock.return_value = {
                "metaData": {
                    "docDate": "2023-01-01",
                    "senderName": "Test Sender",
                    "claim": {
                        "claimNumber": "ABC123",
                    },
                    "claimant": {
                        "claimantName": "John Doe",
                    },
                }
            }
            yield mock

    @pytest.fixture
    def mock_extract_metadata_from_texas_rfa(self):
        """Mock the extract_metadata_from_texas_rfa function."""
        with patch("metadata_extractor.metadata_extractor.extract_metadata_from_texas_rfa") as mock:
            mock.return_value = {
                "metaData": {
                    "docDate": "2023-01-01",
                    "senderName": "Test Sender TX",
                    "claim": {
                        "claimNumber": "TX123",
                    },
                    "claimant": {
                        "claimantName": "Jane Smith",
                    },
                }
            }
            yield mock

    @pytest.fixture
    def mock_extract_metadata_from_physician_bill(self):
        """Mock the extract_metadata_from_physician_bill function."""
        with patch("metadata_extractor.metadata_extractor.extract_metadata_from_physician_bill") as mock:
            mock.return_value = {
                "metaData": {
                    "docDate": "2023-02-01",
                    "senderName": "Dr. Smith",
                    "claim": {
                        "claimNumber": "HCFA123",
                    },
                    "claimant": {
                        "claimantName": "Robert Johnson",
                    },
                }
            }
            yield mock

    @pytest.fixture
    def mock_extract_metadata_from_hospital_bill(self):
        """Mock the extract_metadata_from_hospital_bill function."""
        with patch("metadata_extractor.metadata_extractor.extract_metadata_from_hospital_bill") as mock:
            mock.return_value = {
                "metaData": {
                    "docDate": "2023-03-01",
                    "senderName": "General Hospital",
                    "claim": {
                        "claimNumber": "UB123",
                    },
                    "claimant": {
                        "claimantName": "Mary Williams",
                    },
                }
            }
            yield mock

    @pytest.fixture
    def mock_extract_naming_fields(self):
        """Mock the extract_naming_fields function."""
        with patch("metadata_extractor.metadata_extractor.extract_naming_fields") as mock:
            mock.return_value = {
                "claimNumber": "NAMING123",
                "patientName": "Patient Name",
                "senderName": "Sender Name",
                "docDate": "2023-04-01",
            }
            yield mock

    @pytest.fixture
    def mock_run_subject_line_and_body_extraction(self):
        """Mock the run_subject_line_and_body_extraction function."""
        with patch("metadata_extractor.metadata_extractor.run_subject_line_and_body_extraction") as mock:
            mock.return_value = {
                "subjectLine": "Test Subject",
                "bodyText": "Test Body",
            }
            yield mock

    @pytest.fixture
    def mock_run_summary_extraction(self):
        """Mock the run_summary_extraction function."""
        with patch("metadata_extractor.metadata_extractor.run_summary_extraction") as mock:
            mock.return_value = {
                "summary": "Test Summary",
            }
            yield mock

    @pytest.fixture
    def mock_classify_language(self):
        """Mock the classify_language function."""
        with patch("metadata_extractor.metadata_extractor.classify_language") as mock:
            mock.return_value = True  # English
            yield mock

    @pytest.fixture
    def mock_classify_handwriting(self):
        """Mock the classify_handwriting function."""
        with patch("metadata_extractor.metadata_extractor.classify_handwriting") as mock:
            mock.return_value = False  # Not handwritten
            yield mock

    @pytest.fixture
    def mock_fuzz(self):
        """Mock the fuzz.partial_ratio function."""
        with patch("metadata_extractor.metadata_extractor.fuzz.partial_ratio") as mock:
            mock.return_value = 85  # Above threshold for California RFA
            yield mock

    @pytest.fixture
    def mock_californian_keywords(self):
        """Mock the californian_keywords list."""
        with patch("metadata_extractor.metadata_extractor.californian_keywords", ["california", "ca"]):
            yield

    def test_extract_metadata_rfa_california(
        self,
        mock_splitted_document,
        mock_pdf_file,
        mock_jsonformat,
        mock_fill_mapped_fields,
        mock_extract_metadata_from_rfa,
        mock_fuzz,
        mock_californian_keywords,
        mock_classify_language,
        mock_classify_handwriting,
    ):
        """Test extracting metadata from a California RFA document."""
        # Arrange
        document_type = "RFA"

        # Act
        result = extract_metadata(mock_splitted_document, document_type, mock_pdf_file)

        # Assert
        mock_jsonformat.GetFieldsForDocument.assert_called_once_with(mock_splitted_document.document_type)
        mock_extract_metadata_from_rfa.assert_called_once_with(
            mock_splitted_document.text_ocr, mock_pdf_file
        )
        mock_fill_mapped_fields.assert_called_once()
        mock_classify_language.assert_called_once_with(mock_pdf_file)
        mock_classify_handwriting.assert_called_once_with(mock_pdf_file)

        # Check that the result contains the expected data
        assert "metaData" in result
        assert "namingData" in result
        assert "pagesRef" in result
        assert result["pagesRef"] == mock_splitted_document.parent_document_pages

        # Check that the metadata was expanded properly
        assert isinstance(result["metaData"]["docDate"], dict)
        assert "value" in result["metaData"]["docDate"]
        assert "confidence" in result["metaData"]["docDate"]
        assert "valid" in result["metaData"]["docDate"]
        assert "required" in result["metaData"]["docDate"]

    def test_extract_metadata_rfa_texas(
        self,
        mock_splitted_document,
        mock_pdf_file,
        mock_jsonformat,
        mock_fill_mapped_fields,
        mock_extract_metadata_from_texas_rfa,
        mock_fuzz,
        mock_californian_keywords,
        mock_classify_language,
        mock_classify_handwriting,
    ):
        """Test extracting metadata from a Texas RFA document."""
        # Arrange
        document_type = "RFA"
        # Make the fuzz ratio below threshold to trigger Texas RFA
        mock_fuzz.return_value = 70

        # Act
        result = extract_metadata(mock_splitted_document, document_type, mock_pdf_file)

        # Assert
        mock_jsonformat.GetFieldsForDocument.assert_called_once_with(mock_splitted_document.document_type)
        mock_extract_metadata_from_texas_rfa.assert_called_once_with(
            mock_splitted_document.text_ocr, mock_pdf_file
        )
        mock_fill_mapped_fields.assert_called_once()

        # Check that the result contains the expected data
        assert "metaData" in result
        assert "namingData" in result
        assert "pagesRef" in result

    def test_extract_metadata_physician_bill(
        self,
        mock_splitted_document,
        mock_pdf_file,
        mock_jsonformat,
        mock_fill_mapped_fields,
        mock_extract_metadata_from_physician_bill,
        mock_classify_language,
        mock_classify_handwriting,
    ):
        """Test extracting metadata from a Physician Bill document."""
        # Arrange
        document_type = "Physician Bill (HCFA)"
        mock_splitted_document.document_type = document_type

        # Act
        result = extract_metadata(mock_splitted_document, document_type, mock_pdf_file)

        # Assert
        mock_jsonformat.GetFieldsForDocument.assert_called_once_with(mock_splitted_document.document_type)
        mock_extract_metadata_from_physician_bill.assert_called_once_with(
            mock_splitted_document.text_ocr, mock_pdf_file
        )
        mock_fill_mapped_fields.assert_called_once()

        # Check that the result contains the expected data
        assert "metaData" in result
        assert "namingData" in result
        assert "pagesRef" in result

    def test_extract_metadata_hospital_bill(
        self,
        mock_splitted_document,
        mock_pdf_file,
        mock_jsonformat,
        mock_fill_mapped_fields,
        mock_extract_metadata_from_hospital_bill,
        mock_classify_language,
        mock_classify_handwriting,
    ):
        """Test extracting metadata from a Hospital Bill document."""
        # Arrange
        document_type = "Hospital Bill (UB)"
        mock_splitted_document.document_type = document_type

        # Act
        result = extract_metadata(mock_splitted_document, document_type, mock_pdf_file)

        # Assert
        mock_jsonformat.GetFieldsForDocument.assert_called_once_with(mock_splitted_document.document_type)
        mock_extract_metadata_from_hospital_bill.assert_called_once_with(
            mock_splitted_document.text_ocr, mock_pdf_file
        )
        mock_fill_mapped_fields.assert_called_once()

        # Check that the result contains the expected data
        assert "metaData" in result
        assert "namingData" in result
        assert "pagesRef" in result

    def test_extract_metadata_fax(
        self,
        mock_splitted_document,
        mock_pdf_file,
        mock_jsonformat,
        mock_fill_mapped_fields,
        mock_extract_naming_fields,
        mock_run_subject_line_and_body_extraction,
        mock_classify_language,
        mock_classify_handwriting,
    ):
        """Test extracting metadata from a Fax document."""
        # Arrange
        document_type = "Fax"
        mock_splitted_document.document_type = document_type

        # Act
        result = extract_metadata(mock_splitted_document, document_type, mock_pdf_file)

        # Assert
        mock_jsonformat.GetFieldsForDocument.assert_called_once_with(mock_splitted_document.document_type)
        mock_extract_naming_fields.assert_called_once_with(
            mock_splitted_document.text_ocr, document_type
        )
        mock_run_subject_line_and_body_extraction.assert_called_once_with(mock_pdf_file)
        mock_fill_mapped_fields.assert_called_once()

        # Check that the result contains the expected data
        assert "metaData" in result
        assert "namingData" in result
        assert "pagesRef" in result

    def test_extract_metadata_medical_records(
        self,
        mock_splitted_document,
        mock_pdf_file,
        mock_jsonformat,
        mock_fill_mapped_fields,
        mock_extract_naming_fields,
        mock_run_summary_extraction,
        mock_classify_language,
        mock_classify_handwriting,
    ):
        """Test extracting metadata from a Medical Records document."""
        # Arrange
        document_type = "Medical Records"
        mock_splitted_document.document_type = document_type

        # Act
        result = extract_metadata(mock_splitted_document, document_type, mock_pdf_file)

        # Assert
        mock_jsonformat.GetFieldsForDocument.assert_called_once_with(mock_splitted_document.document_type)
        mock_extract_naming_fields.assert_called_once_with(
            mock_splitted_document.text_ocr, document_type
        )
        mock_run_summary_extraction.assert_called_once_with(
            mock_splitted_document.text_ocr, document_type, mock_pdf_file
        )
        mock_fill_mapped_fields.assert_called_once()

        # Check that the result contains the expected data
        assert "metaData" in result
        assert "namingData" in result
        assert "pagesRef" in result

    def test_extract_metadata_supplemental_work_status(
        self,
        mock_splitted_document,
        mock_pdf_file,
        mock_jsonformat,
        mock_fill_mapped_fields,
        mock_extract_naming_fields,
        mock_run_summary_extraction,
        mock_classify_language,
        mock_classify_handwriting,
    ):
        """Test extracting metadata from a Supplemental/Work Status document."""
        # Arrange
        document_type = "Supplemental/Work Status"
        mock_splitted_document.document_type = document_type

        # Act
        result = extract_metadata(mock_splitted_document, document_type, mock_pdf_file)

        # Assert
        mock_jsonformat.GetFieldsForDocument.assert_called_once_with(mock_splitted_document.document_type)
        mock_extract_naming_fields.assert_called_once_with(
            mock_splitted_document.text_ocr, document_type
        )
        mock_run_summary_extraction.assert_called_once_with(
            mock_splitted_document.text_ocr, document_type, mock_pdf_file
        )
        mock_fill_mapped_fields.assert_called_once()

        # Check that the result contains the expected data
        assert "metaData" in result
        assert "namingData" in result
        assert "pagesRef" in result

    def test_extract_metadata_generic_document(
        self,
        mock_splitted_document,
        mock_pdf_file,
        mock_jsonformat,
        mock_fill_mapped_fields,
        mock_extract_naming_fields,
        mock_classify_language,
        mock_classify_handwriting,
    ):
        """Test extracting metadata from a generic document type."""
        # Arrange
        document_type = "Generic Document"
        mock_splitted_document.document_type = document_type

        # Act
        result = extract_metadata(mock_splitted_document, document_type, mock_pdf_file)

        # Assert
        mock_jsonformat.GetFieldsForDocument.assert_called_once_with(mock_splitted_document.document_type)
        mock_extract_naming_fields.assert_called_once_with(
            mock_splitted_document.text_ocr, document_type
        )
        mock_fill_mapped_fields.assert_called_once()

        # Check that the result contains the expected data
        assert "metaData" in result
        assert "namingData" in result
        assert "pagesRef" in result

    def test_extract_metadata_error_handling(
        self,
        mock_splitted_document,
        mock_pdf_file,
        mock_jsonformat,
        mock_fill_mapped_fields,
        mock_classify_language,
        mock_classify_handwriting,
    ):
        """Test error handling in extract_metadata function."""
        # Arrange
        document_type = "RFA"

        # Simulate an exception during metadata extraction
        with patch("metadata_extractor.metadata_extractor.extract_metadata_from_rfa") as mock_extract:
            mock_extract.side_effect = Exception("Test exception")

            # Act
            result = extract_metadata(mock_splitted_document, document_type, mock_pdf_file)

            # Assert
            mock_jsonformat.GetFieldsForDocument.assert_called_once_with(mock_splitted_document.document_type)
            mock_extract.assert_called_once()
            mock_fill_mapped_fields.assert_called_once()

            # Check that the result contains the expected data despite the exception
            assert "metaData" in result
            assert "namingData" in result
            assert "pagesRef" in result

    def test_extract_metadata_summary_extraction_error(
        self,
        mock_splitted_document,
        mock_pdf_file,
        mock_jsonformat,
        mock_fill_mapped_fields,
        mock_extract_naming_fields,
        mock_classify_language,
        mock_classify_handwriting,
    ):
        """Test handling of SystemExit exception during summary extraction."""
        # Arrange
        document_type = "Medical Records"
        mock_splitted_document.document_type = document_type

        # Simulate a SystemExit exception during summary extraction
        with patch("metadata_extractor.metadata_extractor.run_summary_extraction") as mock_summary:
            mock_summary.side_effect = SystemExit("Test system exit")

            # Act
            result = extract_metadata(mock_splitted_document, document_type, mock_pdf_file)

            # Assert
            mock_jsonformat.GetFieldsForDocument.assert_called_once_with(mock_splitted_document.document_type)
            mock_extract_naming_fields.assert_called_once()
            mock_summary.assert_called_once()
            mock_fill_mapped_fields.assert_called_once()

            # Check that the result contains the expected data despite the exception
            assert "metaData" in result
            assert "namingData" in result
            assert "pagesRef" in result

    @pytest.mark.integration
    def test_extract_metadata_with_real_pdf(self):
        """
        Integration test for extract_metadata with a real PDF file.

        Note: This test is marked as an integration test because it uses a real PDF file.
        To run this test, you need to have a sample PDF file in the tests/test_data directory.
        If the file doesn't exist, this test will be skipped.
        """
        # Check if the test data directory exists
        test_data_dir = os.path.join(os.path.dirname(__file__), "test_data")
        if not os.path.exists(test_data_dir):
            pytest.skip("Test data directory does not exist")

        # Check if the sample PDF file exists
        sample_pdf_path = os.path.join(test_data_dir, "sample_rfa.pdf")
        if not os.path.exists(sample_pdf_path):
            pytest.skip("Sample PDF file does not exist")

        # Create a mock splitted document
        mock_doc = Mock()
        mock_doc.text_ocr = "Sample OCR text for testing"
        mock_doc.document_type = "RFA"
        mock_doc.uuid = UUID("12345678-1234-5678-1234-************")
        mock_doc.parent_document_pages = [[1, 2, 3]]

        # Load the PDF file
        with open(sample_pdf_path, "rb") as f:
            pdf_content = f.read()

        # Mock the necessary dependencies
        with patch("metadata_extractor.metadata_extractor.JSONFormat") as mock_jsonformat, \
             patch("metadata_extractor.metadata_extractor.fill_mapped_fields") as mock_fill, \
             patch("metadata_extractor.metadata_extractor.extract_metadata_from_rfa") as mock_extract, \
             patch("metadata_extractor.metadata_extractor.classify_language") as mock_lang, \
             patch("metadata_extractor.metadata_extractor.classify_handwriting") as mock_hw, \
             patch("metadata_extractor.metadata_extractor.fuzz.partial_ratio") as mock_fuzz:

            # Set up the mocks
            mock_jsonformat.GetFieldsForDocument.return_value = {
                "docName": "",
                "docType": "RFA",
                "namingData": {},
                "metaData": {},
            }
            mock_jsonformat.metadata_required_fileds = {
                "rfa": {
                    "docDate": "",
                    "senderName": "",
                    "claim": {
                        "claimNumber": "",
                    },
                    "claimant": {
                        "claimantName": "",
                    },
                }
            }
            mock_extract.return_value = {
                "metaData": {
                    "docDate": "2023-01-01",
                    "senderName": "Test Sender",
                    "claim": {
                        "claimNumber": "ABC123",
                    },
                    "claimant": {
                        "claimantName": "John Doe",
                    },
                }
            }
            mock_lang.return_value = True
            mock_hw.return_value = False
            mock_fuzz.return_value = 85

            # Call the function
            result = extract_metadata(mock_doc, "RFA", pdf_content)

            # Assert
            mock_jsonformat.GetFieldsForDocument.assert_called_once()
            mock_extract.assert_called_once()
            mock_fill.assert_called_once()

            # Check that the result contains the expected data
            assert "metaData" in result
            assert "namingData" in result
            assert "pagesRef" in result