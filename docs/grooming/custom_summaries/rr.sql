-- DROP SCHEMA public;

CREATE SCHEMA public AUTHORIZATION pg_database_owner;

COMMENT ON SCHEMA public IS 'standard public schema';
-- public.tenants definition

-- Drop table

-- DROP TABLE public.tenants;

CREATE TABLE public.tenants (
	tenant_id varchar(128) NOT NULL,
	tenant_name varchar(256) NOT NULL,
	description varchar(512) NULL,
	contact_email varchar(256) NULL,
	created_date timestamptz DEFAULT CURRENT_TIMESTAMP NULL,
	updated_date timestamptz DEFAULT CURRENT_TIMESTAMP NULL,
	active bool DEFAULT true NULL,
	tenant_config json NULL,
	CONSTRAINT tenants_pkey PRIMARY KEY (tenant_id)
);

-- Permissions

ALTER TABLE public.tenants OWNER TO backend;
GRANT ALL ON TABLE public.tenants TO backend;


-- public.user_activity_logs definition

-- Drop table

-- DROP TABLE public.user_activity_logs;

CREATE TABLE public.user_activity_logs (
	activity_id uuid NOT NULL,
	user_uuid uuid NULL,
	"action" varchar NULL,
	description varchar NULL,
	action_timestamp timestamptz DEFAULT now() NOT NULL,
	tenant_id varchar(128) NULL,
	subtenant_id varchar(128) NULL,
	CONSTRAINT user_activity_logs_pkey PRIMARY KEY (activity_id)
);
CREATE INDEX idx_activity_log_tenant ON public.user_activity_logs USING btree (tenant_id, subtenant_id);

-- Permissions

ALTER TABLE public.user_activity_logs OWNER TO backend;
GRANT ALL ON TABLE public.user_activity_logs TO backend;


-- public.users definition

-- Drop table

-- DROP TABLE public.users;

CREATE TABLE public.users (
	user_uuid uuid NOT NULL,
	username text NULL,
	"password" varchar NULL,
	email varchar(50) NULL,
	given_name varchar(60) NULL,
	family_name varchar(60) NULL,
	last_online timestamptz NULL,
	secret_key varchar NULL,
	user_type text NULL,
	current_file text NULL,
	login_attempt int2 NULL,
	active bool NULL,
	password_set timestamptz DEFAULT now() NOT NULL,
	pass_expire_mail int2 NULL,
	otp_requested timestamptz NULL,
	accessible_tenants json NULL,
	default_tenant_id varchar(128) NULL,
	CONSTRAINT users_email_key UNIQUE (email),
	CONSTRAINT users_pkey PRIMARY KEY (user_uuid),
	CONSTRAINT users_username_key UNIQUE (username)
);

-- Permissions

ALTER TABLE public.users OWNER TO backend;
GRANT ALL ON TABLE public.users TO backend;


-- public.incoming_packages definition

-- Drop table

-- DROP TABLE public.incoming_packages;

CREATE TABLE public.incoming_packages (
	"uuid" uuid NOT NULL,
	original_name varchar(256) NULL,
	channel varchar(64) NULL,
	received_date timestamptz NULL,
	status varchar(128) NULL,
	"options" json NULL,
	incoming_data json NULL,
	raw_incoming_package_id uuid NULL,
	tenant_id varchar(128) NULL,
	subtenant_id varchar(128) NULL,
	CONSTRAINT incoming_packages_pkey PRIMARY KEY (uuid),
	CONSTRAINT incoming_packages_tenant_id_fkey FOREIGN KEY (tenant_id) REFERENCES public.tenants(tenant_id)
);
CREATE INDEX idx_incoming_package_status_tenant ON public.incoming_packages USING btree (status, tenant_id, subtenant_id);
CREATE INDEX idx_incoming_package_tenant ON public.incoming_packages USING btree (tenant_id, subtenant_id);

-- Permissions

ALTER TABLE public.incoming_packages OWNER TO backend;
GRANT ALL ON TABLE public.incoming_packages TO backend;


-- public.subtenants definition

-- Drop table

-- DROP TABLE public.subtenants;

CREATE TABLE public.subtenants (
	subtenant_id varchar(128) NOT NULL,
	tenant_id varchar(128) NOT NULL,
	subtenant_name varchar(256) NOT NULL,
	description varchar(512) NULL,
	contact_email varchar(256) NULL,
	created_date timestamptz DEFAULT CURRENT_TIMESTAMP NULL,
	updated_date timestamptz DEFAULT CURRENT_TIMESTAMP NULL,
	active bool DEFAULT true NULL,
	subtenant_config json NULL,
	CONSTRAINT subtenants_pkey PRIMARY KEY (subtenant_id),
	CONSTRAINT unique_subtenant_tenant UNIQUE (tenant_id, subtenant_id),
	CONSTRAINT subtenants_tenant_id_fkey FOREIGN KEY (tenant_id) REFERENCES public.tenants(tenant_id)
);
CREATE INDEX idx_subtenant_tenant ON public.subtenants USING btree (tenant_id, subtenant_id);

-- Permissions

ALTER TABLE public.subtenants OWNER TO backend;
GRANT ALL ON TABLE public.subtenants TO backend;


-- public.tenant_upload_configurations definition

-- Drop table

-- DROP TABLE public.tenant_upload_configurations;

CREATE TABLE public.tenant_upload_configurations (
	id varchar(36) DEFAULT gen_random_uuid()::text NOT NULL -- Unique identifier for the configuration,
	tenant_id varchar(128) NOT NULL -- Tenant ID this configuration belongs to,
	subtenant_id varchar(128) NULL -- Subtenant ID (optional),
	configuration_name varchar(256) NOT NULL -- Human-readable name for this configuration,
	channel_type varchar(50) NOT NULL -- Type of upload channel (sftp, servicebus_topic, servicebus_queue, api_s3, api_rest),
	is_active bool DEFAULT true NOT NULL -- Whether this configuration is active,
	is_default bool DEFAULT false NOT NULL -- Whether this is the default configuration for this tenant/channel combination,
	channel_config jsonb NOT NULL -- JSON configuration specific to the channel type,
	description text NULL -- Description of this configuration,
	created_date timestamptz DEFAULT CURRENT_TIMESTAMP NOT NULL -- When this configuration was created,
	updated_date timestamptz DEFAULT CURRENT_TIMESTAMP NOT NULL -- When this configuration was last updated,
	created_by varchar(128) NULL -- User who created this configuration,
	updated_by varchar(128) NULL -- User who last updated this configuration,
	CONSTRAINT tenant_upload_configurations_pkey PRIMARY KEY (id),
	CONSTRAINT unique_tenant_channel_default UNIQUE (tenant_id, subtenant_id, channel_type, is_default),
	CONSTRAINT fk_tenant_upload_config_subtenant FOREIGN KEY (tenant_id,subtenant_id) REFERENCES public.subtenants(tenant_id,subtenant_id) ON DELETE CASCADE,
	CONSTRAINT fk_tenant_upload_config_tenant FOREIGN KEY (tenant_id) REFERENCES public.tenants(tenant_id) ON DELETE CASCADE
);
CREATE INDEX idx_tenant_upload_config_active ON public.tenant_upload_configurations USING btree (is_active);
CREATE INDEX idx_tenant_upload_config_channel ON public.tenant_upload_configurations USING btree (channel_type);
CREATE INDEX idx_tenant_upload_config_default ON public.tenant_upload_configurations USING btree (tenant_id, subtenant_id, is_default);
CREATE INDEX idx_tenant_upload_config_tenant ON public.tenant_upload_configurations USING btree (tenant_id, subtenant_id);
COMMENT ON TABLE public.tenant_upload_configurations IS 'Stores tenant-specific upload configurations for different channel types';

-- Column comments

COMMENT ON COLUMN public.tenant_upload_configurations.id IS 'Unique identifier for the configuration';
COMMENT ON COLUMN public.tenant_upload_configurations.tenant_id IS 'Tenant ID this configuration belongs to';
COMMENT ON COLUMN public.tenant_upload_configurations.subtenant_id IS 'Subtenant ID (optional)';
COMMENT ON COLUMN public.tenant_upload_configurations.configuration_name IS 'Human-readable name for this configuration';
COMMENT ON COLUMN public.tenant_upload_configurations.channel_type IS 'Type of upload channel (sftp, servicebus_topic, servicebus_queue, api_s3, api_rest)';
COMMENT ON COLUMN public.tenant_upload_configurations.is_active IS 'Whether this configuration is active';
COMMENT ON COLUMN public.tenant_upload_configurations.is_default IS 'Whether this is the default configuration for this tenant/channel combination';
COMMENT ON COLUMN public.tenant_upload_configurations.channel_config IS 'JSON configuration specific to the channel type';
COMMENT ON COLUMN public.tenant_upload_configurations.description IS 'Description of this configuration';
COMMENT ON COLUMN public.tenant_upload_configurations.created_date IS 'When this configuration was created';
COMMENT ON COLUMN public.tenant_upload_configurations.updated_date IS 'When this configuration was last updated';
COMMENT ON COLUMN public.tenant_upload_configurations.created_by IS 'User who created this configuration';
COMMENT ON COLUMN public.tenant_upload_configurations.updated_by IS 'User who last updated this configuration';

-- Table Triggers

create trigger trigger_update_tenant_upload_config_updated_date before
update
    on
    public.tenant_upload_configurations for each row execute function update_tenant_upload_config_updated_date();
create trigger trigger_ensure_single_default_tenant_upload_config before
insert
    or
update
    on
    public.tenant_upload_configurations for each row execute function ensure_single_default_tenant_upload_config();

-- Permissions

ALTER TABLE public.tenant_upload_configurations OWNER TO backend;
GRANT ALL ON TABLE public.tenant_upload_configurations TO backend;


-- public.documents definition

-- Drop table

-- DROP TABLE public.documents;

CREATE TABLE public.documents (
	"uuid" uuid NOT NULL,
	incoming_package_uuid uuid NULL,
	file varchar(256) NULL,
	status varchar(128) NULL,
	last_opened timestamptz DEFAULT now() NULL,
	"date" timestamptz NULL,
	uploaded timestamptz NULL,
	text_ocr json NULL,
	initial_predictions_ml json NULL,
	initial_predictions_header_ml json NULL,
	predictions_ml json NULL,
	predictions_ml_time timestamptz NULL,
	predictions_human json NULL,
	predictions_human_time timestamptz NULL,
	metadata_ml json NULL,
	metadata_ml_time timestamptz NULL,
	metadata_human json NULL,
	metadata_human_time timestamptz NULL,
	"comment" varchar(1024) NULL,
	validation_report json NULL,
	file_size int4 NULL,
	pages_count int4 NULL,
	document_end_page int4 NULL,
	tenant_id varchar(128) NULL,
	subtenant_id varchar(128) NULL,
	CONSTRAINT documents_pkey PRIMARY KEY (uuid),
	CONSTRAINT documents_incoming_package_uuid_fkey FOREIGN KEY (incoming_package_uuid) REFERENCES public.incoming_packages("uuid"),
	CONSTRAINT documents_tenant_id_fkey FOREIGN KEY (tenant_id) REFERENCES public.tenants(tenant_id)
);
CREATE INDEX idx_document_status_tenant ON public.documents USING btree (status, tenant_id, subtenant_id);
CREATE INDEX idx_document_tenant ON public.documents USING btree (tenant_id, subtenant_id);

-- Permissions

ALTER TABLE public.documents OWNER TO backend;
GRANT ALL ON TABLE public.documents TO backend;


-- public.splitted_documents definition

-- Drop table

-- DROP TABLE public.splitted_documents;

CREATE TABLE public.splitted_documents (
	"uuid" uuid NOT NULL,
	parent_document_uuid uuid NULL,
	parent_document_pages json NULL,
	classification_confidence int4 NULL,
	overall_confidence int4 NULL,
	document_type varchar(128) NULL,
	file varchar(256) NULL,
	status varchar(128) NULL,
	splitted timestamptz NULL,
	last_opened timestamptz DEFAULT now() NULL,
	text_ocr json NULL,
	metadata_ml json NULL,
	metadata_ml_time timestamptz NULL,
	metadata_human json NULL,
	metadata_human_time timestamptz NULL,
	"comment" varchar(1024) NULL,
	file_size int4 NULL,
	pages_count int4 NULL,
	tenant_id varchar(128) NULL,
	subtenant_id varchar(128) NULL,
	CONSTRAINT splitted_documents_pkey PRIMARY KEY (uuid),
	CONSTRAINT splitted_documents_parent_document_uuid_fkey FOREIGN KEY (parent_document_uuid) REFERENCES public.documents("uuid"),
	CONSTRAINT splitted_documents_tenant_id_fkey FOREIGN KEY (tenant_id) REFERENCES public.tenants(tenant_id)
);
CREATE INDEX idx_splitted_document_status_tenant ON public.splitted_documents USING btree (status, tenant_id, subtenant_id);
CREATE INDEX idx_splitted_document_tenant ON public.splitted_documents USING btree (tenant_id, subtenant_id);
CREATE INDEX idx_splitted_document_type_tenant ON public.splitted_documents USING btree (document_type, tenant_id, subtenant_id);

-- Permissions

ALTER TABLE public.splitted_documents OWNER TO backend;
GRANT ALL ON TABLE public.splitted_documents TO backend;


-- public.user_documents definition

-- Drop table

-- DROP TABLE public.user_documents;

CREATE TABLE public.user_documents (
	user_uuid uuid NOT NULL,
	document_uuid uuid NOT NULL,
	first_opened timestamptz DEFAULT now() NULL,
	last_opened timestamptz DEFAULT now() NOT NULL,
	validated timestamptz NULL,
	delegated timestamptz NULL,
	details varchar(50) NULL,
	CONSTRAINT user_documents_pkey PRIMARY KEY (user_uuid, document_uuid),
	CONSTRAINT user_documents_document_uuid_fkey FOREIGN KEY (document_uuid) REFERENCES public.documents("uuid"),
	CONSTRAINT user_documents_user_uuid_fkey FOREIGN KEY (user_uuid) REFERENCES public.users(user_uuid)
);

-- Permissions

ALTER TABLE public.user_documents OWNER TO backend;
GRANT ALL ON TABLE public.user_documents TO backend;


-- public.doc_comments definition

-- Drop table

-- DROP TABLE public.doc_comments;

CREATE TABLE public.doc_comments (
	doc_comment_id uuid NOT NULL,
	document_uuid uuid NULL,
	left_by_user uuid NULL,
	left_at timestamptz DEFAULT now() NOT NULL,
	"text" varchar(1000) NULL,
	CONSTRAINT doc_comments_pkey PRIMARY KEY (doc_comment_id),
	CONSTRAINT doc_comments_document_uuid_fkey FOREIGN KEY (document_uuid) REFERENCES public.documents("uuid") ON DELETE CASCADE,
	CONSTRAINT doc_comments_left_by_user_fkey FOREIGN KEY (left_by_user) REFERENCES public.users(user_uuid)
);

-- Permissions

ALTER TABLE public.doc_comments OWNER TO backend;
GRANT ALL ON TABLE public.doc_comments TO backend;


-- public.document_chunk definition

-- Drop table

-- DROP TABLE public.document_chunk;

CREATE TABLE public.document_chunk (
	"uuid" uuid NOT NULL,
	incoming_package_uuid uuid NULL,
	parent_document_uuid uuid NULL,
	file varchar(256) NULL,
	last_opened timestamptz DEFAULT now() NULL,
	"date" timestamptz NULL,
	status varchar(128) NULL,
	text_ocr json NULL,
	parent_document_start_page varchar(128) NULL,
	parent_document_end_page varchar(128) NULL,
	initial_predictions_ml json NULL,
	initial_predictions_header_ml json NULL,
	predictions_ml json NULL,
	predictions_ml_time timestamptz NULL,
	predictions_human json NULL,
	predictions_human_time timestamptz NULL,
	metadata_ml json NULL,
	metadata_ml_time timestamptz NULL,
	metadata_human json NULL,
	metadata_human_time timestamptz NULL,
	"comment" varchar(1024) NULL,
	file_size int4 NULL,
	pages_count int4 NULL,
	tenant_id varchar(128) NULL,
	subtenant_id varchar(128) NULL,
	CONSTRAINT document_chunk_pkey PRIMARY KEY (uuid),
	CONSTRAINT document_chunk_incoming_package_uuid_fkey FOREIGN KEY (incoming_package_uuid) REFERENCES public.incoming_packages("uuid"),
	CONSTRAINT document_chunk_parent_document_uuid_fkey FOREIGN KEY (parent_document_uuid) REFERENCES public.documents("uuid"),
	CONSTRAINT document_chunk_tenant_id_fkey FOREIGN KEY (tenant_id) REFERENCES public.tenants(tenant_id)
);
CREATE INDEX idx_document_chunk_status_tenant ON public.document_chunk USING btree (status, tenant_id, subtenant_id);
CREATE INDEX idx_document_chunk_tenant ON public.document_chunk USING btree (tenant_id, subtenant_id);

-- Permissions

ALTER TABLE public.document_chunk OWNER TO backend;
GRANT ALL ON TABLE public.document_chunk TO backend;



-- DROP FUNCTION public.ensure_single_default_tenant_upload_config();

CREATE OR REPLACE FUNCTION public.ensure_single_default_tenant_upload_config()
 RETURNS trigger
 LANGUAGE plpgsql
AS $function$
BEGIN
    -- If this is being set as default, unset other defaults for the same tenant/channel
    IF NEW.is_default = true THEN
        UPDATE tenant_upload_configurations 
        SET is_default = false 
        WHERE tenant_id = NEW.tenant_id 
          AND (subtenant_id = NEW.subtenant_id OR (subtenant_id IS NULL AND NEW.subtenant_id IS NULL))
          AND channel_type = NEW.channel_type 
          AND id != NEW.id;
    END IF;
    
    RETURN NEW;
END;
$function$
;

-- Permissions

ALTER FUNCTION public.ensure_single_default_tenant_upload_config() OWNER TO backend;
GRANT ALL ON FUNCTION public.ensure_single_default_tenant_upload_config() TO backend;

-- DROP FUNCTION public.update_tenant_upload_config_updated_date();

CREATE OR REPLACE FUNCTION public.update_tenant_upload_config_updated_date()
 RETURNS trigger
 LANGUAGE plpgsql
AS $function$
BEGIN
    NEW.updated_date = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$function$
;

-- Permissions

ALTER FUNCTION public.update_tenant_upload_config_updated_date() OWNER TO backend;
GRANT ALL ON FUNCTION public.update_tenant_upload_config_updated_date() TO backend;


-- Permissions

GRANT ALL ON SCHEMA public TO pg_database_owner;
GRANT USAGE ON SCHEMA public TO public;