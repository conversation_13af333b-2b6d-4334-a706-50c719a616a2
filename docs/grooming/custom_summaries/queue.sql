-- DROP SCHEMA public;

CREATE SCHEMA public AUTHORIZATION pg_database_owner;

COMMENT ON SCHEMA public IS 'standard public schema';
-- public.raw_incoming_packages definition

-- Drop table

-- DROP TABLE public.raw_incoming_packages;

CREATE TABLE public.raw_incoming_packages (
	id uuid DEFAULT gen_random_uuid() NOT NULL,
	name_of_file varchar NULL,
	"object" bytea NULL,
	channel varchar NULL,
	message jsonb NULL,
	received_date timestamptz NULL,
	start_processing_date timestamptz NULL,
	end_processing_date timestamptz NULL,
	status varchar NULL,
	tenant_id varchar(128) NULL,
	subtenant_id varchar(128) NULL,
	CONSTRAINT raw_incoming_packages_pkey PRIMARY KEY (id)
);
CREATE INDEX idx_raw_incoming_status_tenant ON public.raw_incoming_packages USING btree (status, tenant_id, subtenant_id);
CREATE INDEX idx_raw_incoming_tenant ON public.raw_incoming_packages USING btree (tenant_id, subtenant_id);

-- Permissions

ALTER TABLE public.raw_incoming_packages OWNER TO backend;
GRANT ALL ON TABLE public.raw_incoming_packages TO backend;


-- public.tenant_configurations definition

-- Drop table

-- DROP TABLE public.tenant_configurations;

CREATE TABLE public.tenant_configurations (
	id uuid DEFAULT gen_random_uuid() NOT NULL,
	tenant_id varchar(255) NOT NULL,
	subtenant_id varchar(255) NULL,
	configuration_key varchar(255) NOT NULL,
	configuration_value jsonb NOT NULL,
	configuration_type varchar(100) DEFAULT 'user'::character varying NOT NULL,
	"version" int4 DEFAULT 1 NOT NULL,
	is_active bool DEFAULT true NOT NULL,
	created_date timestamptz DEFAULT CURRENT_TIMESTAMP NOT NULL,
	modified_date timestamptz DEFAULT CURRENT_TIMESTAMP NOT NULL,
	created_by varchar(255) NULL,
	modified_by varchar(255) NULL,
	description text NULL,
	CONSTRAINT tenant_configurations_pkey PRIMARY KEY (id)
);

-- Permissions

ALTER TABLE public.tenant_configurations OWNER TO backend;
GRANT ALL ON TABLE public.tenant_configurations TO backend;


-- public.tenants definition

-- Drop table

-- DROP TABLE public.tenants;

CREATE TABLE public.tenants (
	tenant_id varchar(128) NOT NULL,
	tenant_name varchar(256) NOT NULL,
	description varchar(512) NULL,
	contact_email varchar(256) NULL,
	created_date timestamptz DEFAULT CURRENT_TIMESTAMP NULL,
	updated_date timestamptz DEFAULT CURRENT_TIMESTAMP NULL,
	active bool DEFAULT true NULL,
	tenant_config json NULL,
	CONSTRAINT tenants_pkey PRIMARY KEY (tenant_id)
);

-- Permissions

ALTER TABLE public.tenants OWNER TO backend;
GRANT ALL ON TABLE public.tenants TO backend;


-- public.subtenants definition

-- Drop table

-- DROP TABLE public.subtenants;

CREATE TABLE public.subtenants (
	subtenant_id varchar(128) NOT NULL,
	tenant_id varchar(128) NOT NULL,
	subtenant_name varchar(256) NOT NULL,
	description varchar(512) NULL,
	contact_email varchar(256) NULL,
	created_date timestamptz DEFAULT CURRENT_TIMESTAMP NULL,
	updated_date timestamptz DEFAULT CURRENT_TIMESTAMP NULL,
	active bool DEFAULT true NULL,
	subtenant_config json NULL,
	CONSTRAINT subtenants_pkey PRIMARY KEY (subtenant_id),
	CONSTRAINT subtenants_tenant_id_fkey FOREIGN KEY (tenant_id) REFERENCES public.tenants(tenant_id)
);

-- Permissions

ALTER TABLE public.subtenants OWNER TO backend;
GRANT ALL ON TABLE public.subtenants TO backend;




-- Permissions

GRANT ALL ON SCHEMA public TO pg_database_owner;
GRANT USAGE ON SCHEMA public TO public;